import React, { useState, use<PERSON><PERSON>back, useEffect } from 'react';
import { Input } from './ui/Input';
import { Textarea } from './ui/Textarea';
import { Select } from './ui/Select';
import { Button } from './ui/Button';
import { ApplicationData, FormStatus } from '../types';
import { UploadIcon, CheckCircleIcon, ExclamationTriangleIcon, SpinnerIcon, CheckIcon } from './icons/Icons';
import ProgressBar from './ProgressBar';

// For TypeScript: declare the confetti function on the window object
declare global {
  interface Window {
    confetti: (opts: any) => void;
  }
}

const POSITIONS = [
  { value: "", label: "Select a position", disabled: true },
  { value: "front-desk-associate", label: "Front Desk Associate" },
  { value: "assistant-director", label: "Assistant to the Spa Director" },
  { value: "spa-director", label: "Spa Director" },
  { value: "esthetician", label: "Esthetician" },
  { value: "laser-technician", label: "Laser Technician (experience required)" },
  { value: "massage-therapist", label: "Massage Therapist" },
];

const LOCATIONS = [
  { value: "", label: "Select a location", disabled: true },
  { value: "ny-flatiron", label: "NY Flatiron" },
  { value: "ny-midtown", label: "NY Midtown" },
  { value: "ny-mideast", label: "NY MidEast" },
  { value: "ny-soho", label: "NY SoHo" },
  { value: "ny-uws", label: "NY UWS" },
  { value: "ny-any", label: "Any NY Location" },
  { value: "ma-back-bay", label: "MA Boston Back Bay" },
  { value: "ma-north-station", label: "MA Boston North Station" },
  { value: "ma-any", label: "Any MA Location" },
  { value: "fl-miami-beach", label: "FL Miami Beach" },
  { value: "fl-miami-midtown", label: "FL Miami Midtown" },
  { value: "fl-any", label: "Any FL Location" },
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];

const STEPS = ["Personal Information", "Location", "Position", "Availability", "Experience"];

const RecruitmentForm: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Omit<ApplicationData, 'resume'>>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    position: '',
    location: '',
    availability: '',
  });
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [skipResume, setSkipResume] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [status, setStatus] = useState<FormStatus>(FormStatus.Idle);
  const [errors, setErrors] = useState<Partial<Record<keyof ApplicationData, string>>>({});
  
  useEffect(() => {
    if (status === FormStatus.Success && typeof window.confetti === 'function') {
      const confetti = window.confetti;
      const colors = ['#bc9a64', '#d1d5db', '#fefefe', '#8f754b'];

      const fire = (particleRatio: number, opts: object) => {
        confetti({
          origin: { y: 0.7 },
          ...opts,
          particleCount: Math.floor(200 * particleRatio),
          colors: colors,
        });
      }

      fire(0.25, { spread: 26, startVelocity: 55 });
      fire(0.2, { spread: 60 });
      fire(0.35, { spread: 100, decay: 0.91, scalar: 0.8 });
      fire(0.1, { spread: 120, startVelocity: 25, decay: 0.92, scalar: 1.2 });
      fire(0.1, { spread: 120, startVelocity: 45 });
    }
  }, [status]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name as keyof ApplicationData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  }, [errors]);
  
  const validateAndSetFile = (file: File | null) => {
    if (!file) {
      setResumeFile(null);
      return;
    }

    setErrors(prev => ({ ...prev, resume: undefined }));

    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      setErrors(prev => ({ ...prev, resume: "Invalid file type. Please upload a PDF, DOC, or DOCX." }));
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      setErrors(prev => ({ ...prev, resume: `File is too large. Maximum size is 10MB.` }));
      return;
    }
    
    setResumeFile(file);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    validateAndSetFile(e.target.files?.[0] ?? null);
     // Reset file input value to allow re-uploading the same file
    e.target.value = '';
  };
  
  const handleRemoveFile = () => {
    setResumeFile(null);
    setErrors(prev => ({ ...prev, resume: undefined }));
  };
  
  const handleDragEvents = (e: React.DragEvent<HTMLDivElement>, action: 'enter' | 'leave' | 'over' | 'drop') => {
    e.preventDefault();
    e.stopPropagation();
    if (skipResume) return;

    if (action === 'enter' || action === 'over') {
      setIsDragging(true);
    } else if (action === 'leave' || action === 'drop') {
      setIsDragging(false);
    }
    
    if (action === 'drop') {
      validateAndSetFile(e.dataTransfer.files?.[0] ?? null);
    }
  };

  const handleSkipResumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setSkipResume(isChecked);
    if (isChecked) {
      handleRemoveFile();
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Partial<Record<keyof ApplicationData, string>> = {};
    switch (step) {
      case 1:
        const nameRegex = /^[a-zA-Z'-\s]+$/;
        if (!formData.firstName.trim()) {
          newErrors.firstName = "First name is required.";
        } else if (!nameRegex.test(formData.firstName)) {
          newErrors.firstName = "Please enter a valid name.";
        }

        if (!formData.lastName.trim()) {
          newErrors.lastName = "Last name is required.";
        } else if (!nameRegex.test(formData.lastName)) {
          newErrors.lastName = "Please enter a valid name.";
        }
        
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!formData.email.trim()) {
          newErrors.email = "Email is required.";
        } else if (!emailRegex.test(formData.email)) {
          newErrors.email = "Please enter a valid email address.";
        }
        
        const phoneDigits = formData.phone.replace(/\D/g, '');
        if (!formData.phone.trim()) {
          newErrors.phone = "Phone number is required.";
        } else if (phoneDigits.length !== 10) {
          newErrors.phone = "Phone number must contain 10 digits.";
        }
        break;
      case 2:
        if (!formData.location) newErrors.location = "Please select a location.";
        break;
      case 3:
        if (!formData.position) newErrors.position = "Please select a position.";
        break;
      case 4:
        if (!formData.availability.trim()) newErrors.availability = "Please indicate your availability.";
        break;
      case 5:
        if (!skipResume && !resumeFile) {
          newErrors.resume = "Please upload your resume.";
        } else if (errors.resume) { // Carry over existing file validation errors
          newErrors.resume = errors.resume;
        }
        break;
      default:
        break;
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      if (currentStep < STEPS.length) {
        setCurrentStep(prev => prev + 1);
      }
    }
  };

  const handlePrev = () => {
    setErrors({});
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateStep(currentStep)) return;

    setStatus(FormStatus.Loading);

    const submissionData = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      submissionData.append(key, value);
    });
    if (resumeFile) {
      submissionData.append('resume', resumeFile);
    }
    
    console.log("Submitting data:", { ...formData, resume: resumeFile?.name });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    if (Math.random() > 0.1) {
      setStatus(FormStatus.Success);
    } else {
      setStatus(FormStatus.Error);
    }
  };

  const resetForm = () => {
    setFormData({
      firstName: '', lastName: '', email: '', phone: '', position: '', location: '', availability: '',
    });
    handleRemoveFile();
    setSkipResume(false);
    setErrors({});
    setCurrentStep(1);
    setStatus(FormStatus.Idle);
  };
  
  if (status === FormStatus.Success) {
    return (
      <div className="bg-brand-light p-8 rounded-lg shadow-lg text-center animate-fade-in">
          <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-brand-dark">Application Received!</h2>
          <p className="text-neutral-600 mt-2">Thank you for your interest. We've received your application and will get back to you shortly if you're a good fit.</p>
          <Button onClick={resetForm} className="mt-6 w-auto mx-auto">Submit Another Application</Button>
      </div>
    );
  }

  return (
    <div className="bg-brand-light p-6 sm:p-10 rounded-2xl shadow-lg border border-neutral-200/80">
      <ProgressBar currentStep={currentStep} steps={STEPS} />
      <form onSubmit={handleSubmit} noValidate>
        <div className="min-h-[350px]">
          {currentStep === 1 && (
            <div className="space-y-8 animate-fade-in">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <Input label="First Name" name="firstName" value={formData.firstName} onChange={handleInputChange} error={errors.firstName} required />
                <Input label="Last Name" name="lastName" value={formData.lastName} onChange={handleInputChange} error={errors.lastName} required />
              </div>
              <Input label="Email Address" name="email" type="email" value={formData.email} onChange={handleInputChange} error={errors.email} placeholder="<EMAIL>" required />
              <Input label="Phone Number" name="phone" type="tel" value={formData.phone} onChange={handleInputChange} error={errors.phone} placeholder="XXX-XXX-XXXX" required />
            </div>
          )}

          {currentStep === 2 && (
            <div className="animate-fade-in">
              <Select label="Preferred Location" name="location" value={formData.location} onChange={handleInputChange} options={LOCATIONS} error={errors.location} required />
            </div>
          )}

          {currentStep === 3 && (
             <div className="animate-fade-in">
                <Select label="Applying for Position" name="position" value={formData.position} onChange={handleInputChange} options={POSITIONS} error={errors.position} required />
            </div>
          )}
          
          {currentStep === 4 && (
             <div className="animate-fade-in">
                <Textarea 
                  label="Availability" 
                  name="availability" 
                  value={formData.availability} 
                  onChange={handleInputChange} 
                  error={errors.availability} 
                  required 
                  placeholder="e.g., Full-time, Part-time, Mon-Fri 9am-5pm"
                  rows={5}
                />
            </div>
          )}

          {currentStep === 5 && (
            <div className="animate-fade-in space-y-6">
              <div>
                <label className={`block text-sm font-medium mb-1 ${skipResume ? 'text-neutral-400' : 'text-neutral-700'}`}>
                  Resume / CV
                  {!skipResume && <span className="text-red-500">*</span>}
                </label>
                <div className="mt-2">
                  {resumeFile ? (
                    <div className="flex items-center justify-between rounded-lg border border-neutral-300 bg-neutral-50 p-3">
                      <div className="flex items-center gap-2 text-sm font-medium text-green-700">
                        <CheckIcon className="h-5 w-5 flex-shrink-0" />
                        <span className="truncate">{resumeFile.name}</span>
                      </div>
                      <Button type="button" variant="secondary" onClick={handleRemoveFile} className="px-3 py-1 text-xs">Remove</Button>
                    </div>
                  ) : (
                    <div
                      onDragEnter={(e) => handleDragEvents(e, 'enter')}
                      onDragLeave={(e) => handleDragEvents(e, 'leave')}
                      onDragOver={(e) => handleDragEvents(e, 'over')}
                      onDrop={(e) => handleDragEvents(e, 'drop')}
                      className={`relative flex justify-center rounded-lg border border-dashed px-6 py-10 transition-colors duration-200
                        ${skipResume ? 'bg-neutral-100 cursor-not-allowed border-neutral-200' : ''}
                        ${errors.resume && !skipResume ? 'border-red-500 bg-red-50' : 'border-neutral-300'}
                        ${isDragging ? 'border-brand-gold bg-amber-50' : ''}
                      `}
                    >
                      <div className="text-center">
                        <UploadIcon className={`mx-auto h-12 w-12 ${skipResume ? 'text-neutral-300' : 'text-neutral-400'}`} aria-hidden="true" />
                        <div className="mt-4 flex text-sm leading-6 text-neutral-600">
                          <label htmlFor="resume-upload" className={`relative rounded-md bg-transparent font-semibold ${skipResume ? 'text-neutral-400 cursor-not-allowed' : 'text-brand-gold focus-within:outline-none focus-within:ring-2 focus-within:ring-brand-gold focus-within:ring-offset-2 hover:text-[#a98959] cursor-pointer'}`}>
                            <span>Upload a file</span>
                            <input id="resume-upload" name="resume" type="file" className="sr-only" onChange={handleFileChange} accept={ALLOWED_FILE_TYPES.join(',')} disabled={skipResume} />
                          </label>
                          <p className="pl-1">or drag and drop</p>
                        </div>
                        <p className={`text-xs leading-5 ${skipResume ? 'text-neutral-400' : 'text-neutral-500'}`}>PDF, DOC, DOCX up to 10MB</p>
                      </div>
                    </div>
                  )}
                </div>
                {errors.resume && !skipResume && <p className="mt-2 text-sm text-red-600" aria-live="polite">{errors.resume}</p>}
              </div>

              <div className="relative flex items-start">
                <div className="flex h-6 items-center">
                  <input
                    id="skip-resume"
                    name="skip-resume"
                    type="checkbox"
                    checked={skipResume}
                    onChange={handleSkipResumeChange}
                    className="h-4 w-4 rounded border-neutral-300 text-brand-gold focus:ring-brand-gold"
                  />
                </div>
                <div className="ml-3 text-sm leading-6">
                  <label htmlFor="skip-resume" className="font-medium text-neutral-700 cursor-pointer">
                    I do not wish to include a resume
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>
        
        <div className="mt-10 flex flex-col-reverse gap-4 sm:flex-row sm:justify-end sm:gap-6">
          {currentStep > 1 && (
            <Button type="button" onClick={handlePrev} variant="secondary" className="w-full sm:w-32">
              Previous
            </Button>
          )}
          {currentStep < STEPS.length ? (
            <Button type="button" onClick={handleNext} className="w-full sm:w-32">
              Next
            </Button>
          ) : (
            <Button type="submit" disabled={status === FormStatus.Loading} className="w-full sm:w-48">
              {status === FormStatus.Loading ? (
                <>
                  <SpinnerIcon className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" />
                  Submitting...
                </>
              ) : 'Submit Application'}
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};

export default RecruitmentForm;