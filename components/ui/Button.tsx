import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
}

export const Button: React.FC<ButtonProps> = ({ children, className, disabled, variant = 'primary', ...props }) => {
  const baseClasses = "flex justify-center items-center rounded-md px-3.5 py-3 text-base font-semibold leading-6 shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 disabled:cursor-not-allowed transition-colors duration-200";

  const variantClasses = {
    primary: "bg-brand-gold text-brand-light hover:bg-[#a98959] focus-visible:outline-brand-gold disabled:bg-neutral-400",
    secondary: "bg-transparent text-brand-dark ring-1 ring-inset ring-neutral-300 hover:bg-neutral-50 focus-visible:outline-brand-gold disabled:bg-transparent disabled:text-neutral-400 disabled:ring-neutral-300"
  };
  
  return (
    <button
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};