import React from 'react';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  error?: string;
}

export const Textarea: React.FC<TextareaProps> = ({ label, id, name, required, error, ...props }) => {
  const textareaId = id || name;
  return (
    <div>
      <label htmlFor={textareaId} className="block text-sm font-medium leading-6 text-brand-dark">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="mt-2">
        <textarea
          id={textareaId}
          name={name}
          required={required}
          className={`block w-full rounded-md border-0 py-2.5 px-3 text-brand-dark shadow-sm ring-1 ring-inset ${error ? 'ring-red-500' : 'ring-neutral-300'} placeholder:text-neutral-400 focus:ring-2 focus:ring-inset focus:ring-brand-gold sm:text-sm sm:leading-6 transition`}
          {...props}
        />
      </div>
      {error && <p className="mt-2 text-sm text-red-600" aria-live="polite">{error}</p>}
    </div>
  );
};