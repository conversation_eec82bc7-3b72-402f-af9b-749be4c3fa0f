import React from 'react';

interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label: string;
  options: SelectOption[];
  error?: string;
}

export const Select: React.FC<SelectProps> = ({ label, id, name, required, options, error, ...props }) => {
  const selectId = id || name;
  return (
    <div>
      <label htmlFor={selectId} className="block text-sm font-medium leading-6 text-brand-dark">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="mt-2">
        <select
          id={selectId}
          name={name}
          required={required}
          className={`block w-full rounded-md border-0 py-2.5 pl-3 pr-10 text-brand-dark shadow-sm ring-1 ring-inset ${error ? 'ring-red-500' : 'ring-neutral-300'} focus:ring-2 focus:ring-inset focus:ring-brand-gold sm:text-sm sm:leading-6 transition`}
          {...props}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value} disabled={option.disabled}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
      {error && <p className="mt-2 text-sm text-red-600" aria-live="polite">{error}</p>}
    </div>
  );
};