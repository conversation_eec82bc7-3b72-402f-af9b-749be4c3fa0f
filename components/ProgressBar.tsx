import React from 'react';
import { CheckIcon } from './icons/Icons';

interface ProgressBarProps {
  currentStep: number;
  steps: string[];
}

const ProgressBar: React.FC<ProgressBarProps> = ({ currentStep, steps }) => {
  return (
    <div className="mb-12 sm:mb-16">
      <ol role="list" className="flex items-start">
        {steps.map((step, stepIdx) => {
          const stepNumber = stepIdx + 1;
          const isCompleted = currentStep > stepNumber;
          const isCurrent = currentStep === stepNumber;

          return (
            <li key={step} className="relative flex-1 flex flex-col items-center justify-start text-center">
              {/* Connector line */}
              {stepIdx > 0 && (
                <div
                  className={`absolute top-4 left-0 w-full h-0.5 -translate-x-1/2 transition-colors duration-300 ${
                    isCompleted || isCurrent ? 'bg-brand-gold' : 'bg-neutral-200'
                  }`}
                  aria-hidden="true"
                />
              )}
              
              <div className="relative flex flex-col items-center z-10 bg-brand-light px-2">
                {/* Step Circle */}
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-bold ${
                    isCompleted
                      ? 'bg-brand-gold text-brand-light'
                      : isCurrent
                      ? 'border-2 border-brand-gold bg-brand-light text-brand-gold'
                      : 'border-2 border-neutral-300 bg-brand-light text-neutral-400'
                  } transition-colors duration-300`}
                >
                  {isCompleted ? <CheckIcon className="h-5 w-5" /> : <span>{stepNumber}</span>}
                </div>
                {/* Step Label */}
                <p
                  className={`hidden sm:block text-xs font-medium mt-2 transition-colors duration-300 ${
                    isCurrent ? 'text-brand-gold' : 'text-neutral-500'
                  }`}
                >
                  {step.toUpperCase()}
                </p>
              </div>
            </li>
          );
        })}
      </ol>
    </div>
  );
};

export default ProgressBar;