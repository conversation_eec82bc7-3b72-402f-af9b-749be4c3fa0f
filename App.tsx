import React from 'react';
import RecruitmentForm from './components/RecruitmentForm';

const App: React.FC = () => {
  return (
    <div className="bg-brand-light min-h-screen antialiased text-brand-dark">
      <div className="relative isolate overflow-hidden">
        <div className="absolute inset-x-0 top-[-10rem] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[-20rem]" aria-hidden="true">
          <div 
            className="relative left-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#bc9a64] to-[#8f754b] opacity-20 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]" 
            style={{
              clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
            }}
          />
        </div>
        <main className="container mx-auto px-4 py-12 sm:py-20">
          <div className="max-w-3xl mx-auto">
            <header className="text-center mb-8 sm:mb-12">
              <img src="https://cdn.shopify.com/s/files/1/0071/6197/0758/files/logo-spa-hire.png?v=1756532094" alt="Skin Spa New York Logo" className="mx-auto h-24 sm:h-28 w-auto mb-6" />
              <h1 className="text-4xl sm:text-5xl font-bold tracking-tight text-brand-dark">Join Our Team</h1>
              <p className="mt-4 text-lg text-neutral-600">
                We're always looking for passionate people to join us. If you're ready to build the future of esthetics, you've come to the right place.
              </p>
            </header>
            
            <RecruitmentForm />
          </div>
        </main>
        
        <footer className="text-center py-8 mt-12">
          <p className="text-neutral-500 text-sm">&copy; {new Date().getFullYear()} Skin Spa New York, LLC. All rights reserved.</p>
        </footer>
      </div>
    </div>
  );
};

export default App;